window.__MAIN__ = {
  server: {
    // 网关地址
    gateway: 'http://***********:30007/',
    // SSO验证接口
    ssoLoginPath: 'http://*************:28004/ftc-auth-service/web/authorization',
    // SSO登录appid
    ssoAppId: 'dc097ef4b40c43c1b129cef0db3ada49',
    // portal自身地址，用于sso的redirect_url
    selfHost: 'http://*************:38110',
    // 鉴权时使用的header grant-type项
    grantType: 'ebank-client'
  },
  login: {
    // 是否开启登录时的验证码选项
    verification: false,
    // 是否开启SSO登录
    ssoEnable: false,
    // 是否开启证书查询
    certEnable: false,
    // 是否开启核心登录功能，即以_pv_参数等模式进行登录的模式
    coreLoginEnable: true,
    // 单点和菜单集成
    coreLoginSsoBoth: true,
    // 核心模式登录后，返回模块选择页面的地址
    coreLoginWelcomPath: 'http://*************:9087/NASApp/iTreasury-ebank/ebankMain.jsp',
    // 核心登录模式在401后重定向的地址，一般用于定位核心系统的登录页
    coreLoginPath: 'http://*************:9087/NASApp/iTreasury-ebank/Index.jsp',
    // 设置该模块的登录鉴权的参数字段
    authorizationKeys: ['_pv_']
  },
  // 登陆后的定向地址，如果设置只有一项时自动跳转，如果有多个选项时跳转欢迎页面进行选择模块
  apps: [
    {
      name: '网银端',
      entry: 'http://***********:38083/',
      //默认不需填写，如需与idms-frontend部署在统一服务器上，lastIndexOf后面'/ebank',需改成basePath路径加上'/ebank'
      activeRule: function (location) {
        return location.pathname.lastIndexOf('/ebank', 0) === 0;
      },
      activePath: '/ebank'
    }
  ],
  // 后端模块对应的上下文，具体请根据后端配置修改
  context: {
    ftc: 'ftc-ebankvportal-service-merge'
  },
  // 个性化配置
  customization: {
    // logo文件保存路径，一般是相对于public文件夹的路径或者生产环境下与index.html同级路径，如'/logo-huaneng.png'
    logoFile: '',
    // 是否显示业务端
    showTerminal: true,
    // 小图标右侧IDMS文字
    idmsText: 'IDMS',
    // 是否显示头部退出按钮,默认为显示
    showLogout: true,
    // IDMS登陆页密码框是否显示明文切换按钮
    showPassword: true,
    // 自定义浏览器标签名称
    browserTitle: '新一代票据系统',
    // 是否浏览器标签前展示模块名称。如：模块名称 | 新一代票据系统
    showAppName: true
  },
  //默认不需填写，如需与idms-frontend部署在统一服务器上，需填写一个唯一上下文如'idms-portal-ebank'
  basePath: ''
};
