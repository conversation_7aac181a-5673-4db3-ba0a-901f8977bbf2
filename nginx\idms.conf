# set $frontend_ip "*************";
# set $backend_ip "*************";

# 财司portal
server {
  listen 38120;
  location / {
    proxy_redirect off;
    proxy_pass http://*************:38120/;
  }

  location /config/config.js {
    alias ../idms/portal/finance/config.js;
  }
}

# 网银portal
server {
  listen 38110;
  location / {
    proxy_redirect off;
    proxy_pass http://*************:38110/;
  }

  location /config/config.js {
    alias ../idms/portal/ebank/config.js;
  }
}

# mmc app
server {
  listen 38081;
  location / {
    proxy_redirect off;
    proxy_pass http://*************:38081/;
  }

  location /config/config.js {
    alias ../idms/apps/mmc/config.js;
  }
}

# finance app
server {
  listen 38082;
  location / {
    proxy_redirect off;
    proxy_pass http://*************:38082/;
  }

  location /config/config.js {
    alias ../idms/apps/finance/config.js;
  }
}

# ebank app
server {
  listen 38083;
  location / {
    proxy_redirect off;
    proxy_pass http://*************:38083/;
  }

  location /config/config.js {
    alias ../idms/apps/ebank/config.js;
  }
}


# 后端服务地址代理
server {
  listen 30007;
  location / {
    proxy_redirect off;
    proxy_pass http://*************:30007;
  }
}