/* eslint-disable */
function doFilter(data, cb) {
  var arr = [];
  for (var i = 0; i < data.length; i++) {
    if (cb(data[i])) {
      arr.push(data[i]);
    }
  }
  return arr;
}

function _judgeApproval(part, usedFor, appendixType) {
  if (part === 'formItems' || part === 'chooseItems' || part === 'columns') {
    return doFilter(appendixType, function (item) {
      return (
        ['APPLICATION', 'CONTRACT', 'INVOICE', 'OTHER', 'FINANCIAL_STATEMENTS', 'APPROVAL_COMMENTS'].indexOf(
          item.value
        ) !== -1
      );
    });
  }
  return appendixType;
}

function getAppendixType(part, crossBiz, bizTypeInfo, _appendixType, usedFor) {
  var appendixType = JSON.parse(JSON.stringify(_appendixType));
  var normal = doFilter(appendixType, function (item) {
    return ['CONTRACT', 'INVOICE', 'OTHER'].indexOf(item.value) !== -1;
  });
  var target = [
    'INVOICE_APPLY',
    'DISCOUNT_APPLY',
    'DISCOUNT_PRE_APPLY',
    'INVOICE_APPLY_ACCEPTANCE',
    'DISCOUNT_APPLY_ACCEPTANCE'
  ];
  if (crossBiz) {
    var specialFlag = false;
    var normalFlag = false;
    for (var i = 0; i < bizTypeInfo.length; i++) {
      if (target.indexOf(bizTypeInfo[i].businessType) === -1) {
        normalFlag = true;
      } else {
        specialFlag = true;
      }
    }
    if (specialFlag && normalFlag) {
      for (var i = 0; i < appendixType.length; i++) {
        if (normal.indexOf(appendixType[i].value) === -1) {
          appendixType[i].types = target;
        }
      }
      return _judgeApproval(part, usedFor, appendixType);
    }
    return _judgeApproval(part, usedFor, specialFlag ? appendixType : normal);
  } else if (target.indexOf(bizTypeInfo) === -1) {
    return normal;
  }
  return _judgeApproval(part, usedFor, appendixType);
}
function gainAppendixType(part, crossBiz, bizTypeInfo, _appendixType, usedFor) {
  var appendixType = JSON.parse(JSON.stringify(_appendixType));
  var normal = doFilter(appendixType, function (item) {
    return ['CONTRACT', 'INVOICE', 'OTHER', 'TRADE_BACKGROUND_REGISTER'].indexOf(item.value) !== -1;
  });
  return normal;
}

window.__ENV__ = {
  server: {
    // 后台接口网关地址
    apiServer: 'http://***********:30007/',
    // 用于鉴权的header grant-type属性
    grantType: 'finance-client',
    // 是否启用mock url，0关闭，1开启
    mockMode: 0,
    // mock服务对应网关
    mockServer: 'http://10.129.130.35:3000/mock/25/'
  },
  login: {
    // 是否开启核心登录功能，即以_pv_参数等模式进行登录的模式
    coreLoginEnable: true,
    // 核心登录模式在401后重定向的地址，一般用于定位核心系统的登录页
    coreLoginPath: 'http://192.168.1.243:9087/NASApp/Index.jsp',
    // 设置该模块的登录鉴权的参数字段
    authorizationKeys: ['remoteMode', 'loginUrl', 'hessianKey', 'hessianPath']
  },
  app: {
    // 电票是否开启 (默认 是)
    electronic: true,
    // 供票是否开启
    supplyChain: false
  },
  // 后端模块对应的上下文，具体请根据后端配置修改
  context: {
    ftc: 'ftc-vportal-service-merge',
    eamcDiscount: 'idms-application-ebank',
    eamcEndorse: 'idms-application-ebank',
    eamcInvoice: 'idms-application-ebank',
    eamcPledge: 'idms-application-ebank',
    eamcNonDirect: 'idms-application-ebank',
    famcPlatform: 'idms-application-platform',
    financeClearing: 'idms-application-finance',
    financeCoreTransaction: 'idms-application-finance',
    financeDiscount: 'idms-application-finance',
    financeOther: 'idms-application-finance',
    financePledge: 'idms-application-finance',
    financeRecourse: 'idms-application-finance',
    financePaper: 'idms-application-finance',
    generalAttachment: 'idms-application-basic',
    generalBim: 'idms-application-basic',
    generalChargeOff: 'idms-application-finance',
    generalDue: 'idms-application-finance',
    generalGuarantee: 'idms-application-finance',
    generalMessage: 'idms-application-finance',
    generalOther: 'idms-application-finance',
    generalPledge: 'idms-application-finance',
    generalRecourse: 'idms-application-finance',
    generalReply: 'idms-application-finance',
    generalRevocation: 'idms-application-finance',
    generalSetting: 'idms-application-basic',
    generalTransaction: 'idms-application-tx',
    queryEbank: 'idms-application-query',
    queryFinance: 'idms-application-query',
    queryMmc: 'idms-application-query',
    generalData: 'idms-application-basic',
    generalFile: 'idms-application-basic',
    generalTransElementSetting: 'idms-application-basic',
    generalBusinessTracking: 'idms-application-basic',
    workflow: 'workflow-center',
    generalUserExperience: 'idms-application-basic',
    magnifier: 'idms-application-query',
    generalInstructionDistribute: 'idms-application-finance',
    stock: 'idms-application-stock',
    // 贴现申请跨端审批使用，对应网银端贴现申请上下文
    ebankEamcDiscount: 'idms-application-ebank',
    eabnkEamcInvoice: 'idms-application-ebank',
    // 中核扩展贷款业务上下文
    financeLoan: 'idms-application-financeloan',
    ebankLoan: 'idms-application-ebankloan',
    settleApp: 'idms-application-settle',
    generalUidService: 'ftc-uid-service'
  },
  //设置route base
  basePath: '',
  //portal路由路径,默认不需填写，如需与idms-portal在同一服务器上部署，需填写idms-portal的config配置中basePath的路径加上对应端的activePath
  mircoBasePath: '',
  // 审批流相关配置
  workflowConfig: {
    // 财司端与管理中心端合并与否
    mmcMerge: false,
    // 审批流使用的前端服务地址相关，无特殊情况维持默认即可
    frontService: [
      {
        serviceName: '网银端',
        address: 'EBANK'
      },
      {
        serviceName: '财司端',
        address: 'FINANCE'
      },
      {
        serviceName: '管理中心端',
        address: 'MMC'
      }
    ],
    // 跨端查询审批详情页时，对应业务端访问地址，不存在跨端的不要填写
    wfApprovalHost: {
      mmc: 'http://***********:38081/'
    }
  },
  // 数据权限开关
  dataRuleConfig: {
    enableEbank: true,
    enableFinance: false,
    enableMmc: false
  },
  // 粤海个性化区分
  //  isPersonalizeType: false
  //移动审批个性化配置
  // oaUrl: ''
  // 是否开启集成首页 使用场景：不使用产品protal，但是想要产品的首页。false:默认不开启，true:集成开启
  // apptransfer: false,
  // 中核附件个性化配置，附件上传添加申请表、财务报表、审批意见页签，非中核项目需注释该配置
  getAppendixType: getAppendixType,
  // 广汽附件个性化配置，附件上传添加贸易背景资料页签，非广汽项目需注释该配置
  // gainAppendixType: gainAppendixType,
  // 解决合并版部署 详情打印以及代理业务没有样式问题 路径例如:'/idms-frontend/ebank' 不是合并部署可注释
  // themeUrl:''
  // 审批流配置，待审批勾选增加显示票据包总额，默认显示
  showDraftAmount: true,
  // 首页 待审批（approve）待签收（waitSign）待受理（acceptance）数据为0时是否展示, true为默认展示，false为不展示。
  showTab: {
    approve: true,
    waitSign: true,
    acceptance: true
  },
  // 中核附件个性化配置，附件上传添加申请表、财务报表、审批意见页签，非中核项目需注释该配置
  getAppendixType: getAppendixType,
  // 【中核】附件组件的必填项个性化配置（开启后附件组件中部分字段不进行必填校验）。false为不开启，true为开启.
  //  个别功能中的附件组件，可以在idms-draft-appendix上添加personalizationRules属性的true/false值来控制该个性化设置是否生效
  attachmentRulesSetting: false
};
